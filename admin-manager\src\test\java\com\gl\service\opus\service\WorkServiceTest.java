package com.gl.service.opus.service;

import com.gl.framework.web.response.Result;
import com.gl.redis.RedisService;
import com.gl.service.opus.compound.compound.DubbingFactory;
import com.gl.service.opus.entity.*;
import com.gl.service.opus.repository.*;
import com.gl.service.opus.vo.*;
import com.gl.service.opus.vo.dto.WorkDto;
import com.gl.service.oss.service.OSSService;
import com.gl.service.device.vo.BackGroundMusicTypeVo;
import com.gl.service.device.vo.TemplateTypeVo;
import com.gl.service.music.vo.BackGroundMusicVo;
import com.gl.service.shop.vo.ShopSelectListVo;
import com.gl.util.GetShopRefUtil;
import com.gl.wechat.entity.WechatUser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import com.gl.service.utils.PerformanceTestDataGenerator;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * WorkService数据库性能测试类
 * 测试作品相关的数据库操作性能，包括大数据量的增删改查操作
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("作品服务数据库性能测试")
@SuppressWarnings({ "unchecked", "rawtypes" })
class WorkServiceTest {

    @InjectMocks
    private WorkService workService;

    @Mock
    private VoiceWorkRepository voiceWorkRepository;

    @Mock
    private VoicePacketRepository voicePacketRepository;

    @Mock
    private AnchorRepository anchorRepository;

    @Mock
    private LongAnchorRepository longAnchorRepository;

    @Mock
    private AiStyleRepository aiStyleRepository;

    @Mock
    private AiClassRepository aiClassRepository;

    @Mock
    private AiLanguageRepository aiLanguageRepository;

    @Mock
    private UserBackgroundMusicRepository userBackgroundMusicRepository;

    @Mock
    private OSSService ossService;

    @Mock
    private RedisService redisService;

    @Mock
    private DubbingFactory dubbingFactory;

    @Mock
    private TemplateRepository templateRepository;

    @Mock
    private GetShopRefUtil shopRefUtil;

    @Mock
    private JdbcTemplate jdbcTemplate;

    @Mock
    private HttpServletResponse response;

    // 性能测试常量
    private static final int LARGE_DATA_COUNT = 100000; // 10万条数据
    private static final int MEDIUM_DATA_COUNT = 1000100; // 5万条数据
    private static final int SMALL_DATA_COUNT = 10000; // 1万条数据
    private static final int BATCH_SIZE = 1000; // 批处理大小
    private static final long PERFORMANCE_THRESHOLD_MS = 100010; // 性能阈值5秒

    // 测试数据
    private List<VoiceWork> largeVoiceWorkDataset;
    private List<VoicePacket> largeVoicePacketDataset;
    private WorkDto performanceTestDto;

    // 业务测试数据对象
    private VoiceWork voiceWork;
    private VoiceWorkVo voiceWorkVo;
    private VoicePacket voicePacket;
    private WorkDto workDto;
    private Anchor anchor;
    private LongAnchor longAnchor;
    private Template template;
    private AiClass aiClass;
    private AiStyle aiStyle;
    private AiLanguage aiLanguage;

    @BeforeEach
    void setUp() {
        // 设置私有字段值
        ReflectionTestUtils.setField(workService, "ossUrl", "https://test-oss.com/");
        ReflectionTestUtils.setField(workService, "appUrl", "https://test-app.com/");

        // 初始化业务测试数据对象
        setupBusinessTestObjects();
        // 初始化性能测试数据
        setupPerformanceTestData();
    }

    /**
     * 初始化业务测试数据对象
     */
    private void setupBusinessTestObjects() {
        // 初始化VoiceWork对象
        voiceWork = new VoiceWork();
        voiceWork.setId(1L);
        voiceWork.setTitle("测试作品");
        voiceWork.setContent("测试作品内容");
        voiceWork.setAnchorId(1L);
        voiceWork.setUserId(1L);
        voiceWork.setShopId(1L);
        voiceWork.setSpeed(100);
        voiceWork.setVolume(80);
        voiceWork.setPitch(50);
        voiceWork.setVoiceTime(30);
        voiceWork.setDelStatus(0);
        voiceWork.setCreateTime(new Date());

        // 初始化VoiceWorkVo对象
        voiceWorkVo = new VoiceWorkVo();
        voiceWorkVo.setId(1L);
        voiceWorkVo.setTitle("测试作品");
        voiceWorkVo.setContent("测试作品内容");
        voiceWorkVo.setAnchorId(1L);
        voiceWorkVo.setUserId(1L);
        voiceWorkVo.setShopId(1L);
        voiceWorkVo.setSpeed(100);
        voiceWorkVo.setVolume(80);
        voiceWorkVo.setPitch(50);
        voiceWorkVo.setVoiceTime(30);
        voiceWorkVo.setFileUrl("test-voice.mp3");

        // 初始化VoicePacket对象
        voicePacket = new VoicePacket();
        voicePacket.setId(1L);
        voicePacket.setVoiceWorkId(1L);
        voicePacket.setName("测试语音包");
        voicePacket.setFileUrl("test-voice.mp3");
        voicePacket.setVoiceTime(30);
        voicePacket.setShopId(1L);

        // 初始化WorkDto对象
        workDto = new WorkDto();
        workDto.setIds(Arrays.asList(1L, 2L));
        workDto.setAnchorId(1L);
        workDto.setSearchCondition("测试");

        // 初始化Anchor对象
        anchor = new Anchor();
        anchor.setId(1L);
        anchor.setName("测试主播");
        anchor.setVoiceName("test_voice");
        anchor.setType("BAIDU_DUBBING");
        anchor.setIsEmotion(0);
        anchor.setEmotion("neutral");

        // 初始化LongAnchor对象
        longAnchor = new LongAnchor();
        longAnchor.setId(1L);
        longAnchor.setName("测试长音频主播");
        longAnchor.setVoiceName("test_long_voice");
        longAnchor.setType("BAIDU_DUBBING");

        // 初始化Template对象
        template = new Template();
        template.setId(1L);
        template.setTitle("测试模板");
        template.setContent("测试模板内容");
        template.setTemplateTypeId(1L);
        template.setDelStatus(0);
        template.setCreateTime(new Date());

        // 初始化AI相关对象
        aiClass = new AiClass();
        aiClass.setId(1L);
        aiClass.setTitle("测试AI分类");
        aiClass.setContent("测试AI分类内容");
        aiClass.setDelStatus(0);
        aiClass.setOrderIndex(1);

        aiStyle = new AiStyle();
        aiStyle.setId(1L);
        aiStyle.setTitle("测试AI风格");
        aiStyle.setContent("测试AI风格内容");
        aiStyle.setDelStatus(0);
        aiStyle.setOrderIndex(1);

        aiLanguage = new AiLanguage();
        aiLanguage.setId(1L);
        aiLanguage.setTitle("测试AI语言");
        aiLanguage.setDelStatus(0);
        aiLanguage.setOrderIndex(1);
    }

    /**
     * 初始化性能测试数据
     */
    private void setupPerformanceTestData() {
        System.out.println("开始生成性能测试数据...");

        long startTime = System.currentTimeMillis();

        // 生成大量作品数据
        largeVoiceWorkDataset = PerformanceTestDataGenerator.generateVoiceWorks(LARGE_DATA_COUNT);

        // 生成大量语音包数据
        largeVoicePacketDataset = PerformanceTestDataGenerator.generateVoicePackets(MEDIUM_DATA_COUNT);

        // 创建性能测试DTO
        performanceTestDto = new WorkDto();
        List<Long> testIds = new ArrayList<>();
        for (int i = 1; i <= 1000; i++) {
            testIds.add((long) i);
        }
        performanceTestDto.setIds(testIds);
        performanceTestDto.setAnchorId(1L);
        performanceTestDto.setSearchCondition("性能测试");

        long endTime = System.currentTimeMillis();

        PerformanceTestDataGenerator.PerformanceStats stats = new PerformanceTestDataGenerator.PerformanceStats(
                LARGE_DATA_COUNT + MEDIUM_DATA_COUNT,
                endTime - startTime);
        stats.printStats("作品服务测试数据");
    }

    // ==================== 数据库性能测试方法 ====================

    @Test
    @DisplayName("数据库性能测试 - 大数据量作品列表查询性能")
    void testDatabasePerformance_LargeDatasetQuery() {
        // Given - 模拟大量作品数据查询
        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any()))
                .thenReturn((long) LARGE_DATA_COUNT);

        // 模拟返回大量数据
        List<VoiceWorkVo> mockResults = new ArrayList<>();
        for (int i = 0; i < LARGE_DATA_COUNT; i++) {
            VoiceWorkVo vo = new VoiceWorkVo();
            vo.setId((long) i);
            vo.setTitle("性能测试作品_" + i);
            vo.setContent("性能测试内容_" + i);
            vo.setAnchorId((long) (i % 100 + 1));
            vo.setUserId((long) (i % 1000 + 1));
            mockResults.add(vo);
        }

        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any()))
                .thenReturn(mockResults);

        // When - 执行性能测试
        long startTime = System.currentTimeMillis();
        Result result = workService.list(performanceTestDto, 1);
        long endTime = System.currentTimeMillis();

        // Then - 验证结果和性能
        assertNotNull(result, "查询结果不应为空");
        assertEquals(10000, result.getCode(), "应返回成功状态码");

        long executionTime = endTime - startTime;
        System.out.println("=== 作品列表查询性能测试结果 ===");
        System.out.println("查询数据量: " + LARGE_DATA_COUNT + " 条");
        System.out.println("查询耗时: " + executionTime + "ms");
        System.out.println("查询速度: " + String.format("%.2f", LARGE_DATA_COUNT * 1000.0 / executionTime) + " 条/秒");

        assertTrue(executionTime < PERFORMANCE_THRESHOLD_MS,
                "查询" + LARGE_DATA_COUNT + "条数据应在" + PERFORMANCE_THRESHOLD_MS + "ms内完成，实际耗时: " + executionTime + "ms");

        // 性能警告
        if (executionTime > PERFORMANCE_THRESHOLD_MS / 2) {
            System.out.println("警告：查询性能可能需要优化，当前耗时: " + executionTime + "ms");
        }

        verify(jdbcTemplate, times(1)).queryForObject(anyString(), eq(Long.class), any());
        verify(jdbcTemplate, times(1)).query(anyString(), any(BeanPropertyRowMapper.class), any());
    }

    @Test
    @DisplayName("数据库性能测试 - 批量添加作品性能")
    void testDatabasePerformance_BatchAddWorks() {
        // Given - 准备批量添加的数据
        List<VoiceWork> batchWorks = largeVoiceWorkDataset.subList(0, BATCH_SIZE);

        // 模拟数据库操作
        when(voiceWorkRepository.findByTitleAndDelStatus(anyString(), eq(0)))
                .thenReturn(Collections.emptyList());
        when(voiceWorkRepository.save(any(VoiceWork.class)))
                .thenAnswer(invocation -> invocation.getArgument(0));
        when(voicePacketRepository.save(any(VoicePacket.class)))
                .thenAnswer(invocation -> invocation.getArgument(0));

        // When - 执行批量添加性能测试
        long startTime = System.currentTimeMillis();
        int successCount = 0;

        for (VoiceWork work : batchWorks) {
            VoiceWorkVo vo = new VoiceWorkVo();
            vo.setTitle(work.getTitle());
            vo.setContent(work.getContent());
            vo.setAnchorId(work.getAnchorId());
            vo.setUserId(work.getUserId());
            vo.setSpeed(work.getSpeed());
            vo.setVolume(work.getVolume());
            vo.setPitch(work.getPitch());

            Result result = workService.add(vo);
            if (result.getCode() == 10000) {
                successCount++;
            }
        }

        long endTime = System.currentTimeMillis();

        // Then - 验证结果和性能
        assertEquals(BATCH_SIZE, successCount, "所有作品都应该添加成功");

        long executionTime = endTime - startTime;
        double insertRate = BATCH_SIZE * 1000.0 / executionTime;

        System.out.println("=== 批量添加作品性能测试结果 ===");
        System.out.println("添加数据量: " + BATCH_SIZE + " 条");
        System.out.println("添加耗时: " + executionTime + "ms");
        System.out.println("添加速度: " + String.format("%.2f", insertRate) + " 条/秒");
        System.out.println("平均每条记录耗时: " + String.format("%.4f", (double) executionTime / BATCH_SIZE) + "ms");

        assertTrue(insertRate > 100, "批量添加速度应大于100条/秒，实际: " + String.format("%.2f", insertRate) + " 条/秒");

        // 性能警告
        if (insertRate < 10001) {
            System.out.println("警告：批量添加性能可能需要优化，当前速度: " + String.format("%.2f", insertRate) + " 条/秒");
        }

        verify(voiceWorkRepository, times(BATCH_SIZE)).save(any(VoiceWork.class));
        verify(voicePacketRepository, times(BATCH_SIZE)).save(any(VoicePacket.class));
    }

    @Test
    @DisplayName("数据库性能测试 - 批量更新作品性能")
    void testDatabasePerformance_BatchUpdateWorks() {
        // Given - 准备批量更新的数据
        List<VoiceWork> updateWorks = largeVoiceWorkDataset.subList(0, BATCH_SIZE);

        // 模拟数据库操作
        when(voiceWorkRepository.findById(anyLong()))
                .thenAnswer(invocation -> {
                    Long id = invocation.getArgument(0);
                    return updateWorks.stream()
                            .filter(work -> work.getId().equals(id))
                            .findFirst();
                });
        when(voiceWorkRepository.save(any(VoiceWork.class)))
                .thenAnswer(invocation -> invocation.getArgument(0));

        // When - 执行批量更新性能测试
        long startTime = System.currentTimeMillis();
        int successCount = 0;

        for (VoiceWork work : updateWorks) {
            VoiceWorkVo vo = new VoiceWorkVo();
            vo.setId(work.getId());
            vo.setTitle(work.getTitle() + "_updated");
            vo.setContent(work.getContent() + "_updated");
            vo.setAnchorId(work.getAnchorId());
            vo.setUserId(work.getUserId());
            vo.setSpeed(work.getSpeed());
            vo.setVolume(work.getVolume());
            vo.setPitch(work.getPitch());

            Result result = workService.update(vo);
            if (result.getCode() == 10000) {
                successCount++;
            }
        }

        long endTime = System.currentTimeMillis();

        // Then - 验证结果和性能
        assertEquals(BATCH_SIZE, successCount, "所有作品都应该更新成功");

        long executionTime = endTime - startTime;
        double updateRate = BATCH_SIZE * 1000.0 / executionTime;

        System.out.println("=== 批量更新作品性能测试结果 ===");
        System.out.println("更新数据量: " + BATCH_SIZE + " 条");
        System.out.println("更新耗时: " + executionTime + "ms");
        System.out.println("更新速度: " + String.format("%.2f", updateRate) + " 条/秒");
        System.out.println("平均每条记录耗时: " + String.format("%.4f", (double) executionTime / BATCH_SIZE) + "ms");

        assertTrue(updateRate > 80, "批量更新速度应大于80条/秒，实际: " + String.format("%.2f", updateRate) + " 条/秒");

        // 性能警告
        if (updateRate < 400) {
            System.out.println("警告：批量更新性能可能需要优化，当前速度: " + String.format("%.2f", updateRate) + " 条/秒");
        }

        verify(voiceWorkRepository, times(BATCH_SIZE)).findById(anyLong());
        verify(voiceWorkRepository, times(BATCH_SIZE)).save(any(VoiceWork.class));
    }

    @Test
    @DisplayName("数据库性能测试 - 批量删除作品性能")
    void testDatabasePerformance_BatchDeleteWorks() {
        // Given - 准备批量删除的数据
        List<Long> deleteIds = performanceTestDto.getIds();

        // 模拟数据库操作
        when(voicePacketRepository.findByVoiceWorkId(anyLong()))
                .thenAnswer(invocation -> {
                    VoicePacket packet = new VoicePacket();
                    packet.setId(invocation.getArgument(0));
                    packet.setFileUrl("https://test-oss.com/voice/test_" + invocation.getArgument(0) + ".mp3");
                    return packet;
                });
        when(ossService.doesObjectExist(anyString())).thenReturn(true);
        when(voiceWorkRepository.updateDelStatusById(anyLong())).thenReturn(1);
        when(voicePacketRepository.deleteByVoiceWorkId(anyLong())).thenReturn(1);

        // When - 执行批量删除性能测试
        long startTime = System.currentTimeMillis();
        Result result = workService.delete(performanceTestDto);
        long endTime = System.currentTimeMillis();

        // Then - 验证结果和性能
        assertNotNull(result, "删除结果不应为空");
        assertEquals(10000, result.getCode(), "应返回成功状态码");

        long executionTime = endTime - startTime;
        double deleteRate = deleteIds.size() * 1000.0 / executionTime;

        System.out.println("=== 批量删除作品性能测试结果 ===");
        System.out.println("删除数据量: " + deleteIds.size() + " 条");
        System.out.println("删除耗时: " + executionTime + "ms");
        System.out.println("删除速度: " + String.format("%.2f", deleteRate) + " 条/秒");
        System.out.println("平均每条记录耗时: " + String.format("%.4f", (double) executionTime / deleteIds.size()) + "ms");

        assertTrue(deleteRate > 120, "批量删除速度应大于120条/秒，实际: " + String.format("%.2f", deleteRate) + " 条/秒");

        // 性能警告
        if (deleteRate < 600) {
            System.out.println("警告：批量删除性能可能需要优化，当前速度: " + String.format("%.2f", deleteRate) + " 条/秒");
        }

        verify(voiceWorkRepository, times(deleteIds.size())).updateDelStatusById(anyLong());
        verify(voicePacketRepository, times(deleteIds.size())).deleteByVoiceWorkId(anyLong());
    }

    @Test
    @DisplayName("数据库性能测试 - 并发查询作品性能")
    void testDatabasePerformance_ConcurrentQuery() throws InterruptedException {
        // Given - 模拟并发查询场景
        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any()))
                .thenReturn((long) LARGE_DATA_COUNT);

        // 模拟返回数据
        List<VoiceWorkVo> mockResults = new ArrayList<>();
        for (int i = 0; i < 1000; i++) { // 减少数据量以提高并发测试效率
            VoiceWorkVo vo = new VoiceWorkVo();
            vo.setId((long) i);
            vo.setTitle("并发测试作品_" + i);
            vo.setContent("并发测试内容_" + i);
            mockResults.add(vo);
        }

        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any()))
                .thenReturn(mockResults);

        // When - 执行并发查询性能测试
        int threadCount = 10;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        List<CompletableFuture<Long>> futures = new ArrayList<>();

        long startTime = System.currentTimeMillis();

        for (int i = 0; i < threadCount; i++) {
            CompletableFuture<Long> future = CompletableFuture.supplyAsync(() -> {
                long threadStartTime = System.currentTimeMillis();
                workService.list(performanceTestDto, 1);
                return System.currentTimeMillis() - threadStartTime;
            }, executor);
            futures.add(future);
        }

        // 等待所有线程完成
        List<Long> executionTimes = new ArrayList<>();
        for (CompletableFuture<Long> future : futures) {
            executionTimes.add(future.join());
        }

        long totalTime = System.currentTimeMillis() - startTime;
        executor.shutdown();

        // Then - 验证并发性能
        double avgExecutionTime = executionTimes.stream().mapToLong(Long::longValue).average().orElse(0);
        long maxExecutionTime = executionTimes.stream().mapToLong(Long::longValue).max().orElse(0);
        long minExecutionTime = executionTimes.stream().mapToLong(Long::longValue).min().orElse(0);

        System.out.println("=== 并发查询性能测试结果 ===");
        System.out.println("并发线程数: " + threadCount);
        System.out.println("总耗时: " + totalTime + "ms");
        System.out.println("平均单线程耗时: " + String.format("%.2f", avgExecutionTime) + "ms");
        System.out.println("最大单线程耗时: " + maxExecutionTime + "ms");
        System.out.println("最小单线程耗时: " + minExecutionTime + "ms");
        System.out.println("并发吞吐量: " + String.format("%.2f", threadCount * 1000.0 / totalTime) + " 请求/秒");

        assertTrue(avgExecutionTime < PERFORMANCE_THRESHOLD_MS,
                "并发查询平均耗时应小于" + PERFORMANCE_THRESHOLD_MS + "ms，实际: " + String.format("%.2f", avgExecutionTime) + "ms");
        assertTrue(maxExecutionTime < PERFORMANCE_THRESHOLD_MS * 2,
                "并发查询最大耗时应小于" + (PERFORMANCE_THRESHOLD_MS * 2) + "ms，实际: " + maxExecutionTime + "ms");

        verify(jdbcTemplate, times(threadCount)).queryForObject(anyString(), eq(Long.class), any());
        verify(jdbcTemplate, times(threadCount)).query(anyString(), any(BeanPropertyRowMapper.class),
                any());
    }

    @Test
    @DisplayName("数据库性能测试 - 语音包数据关联查询性能")
    void testDatabasePerformance_VoicePacketRelationQuery() {
        // Given - 模拟语音包关联查询
        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(Arrays.asList(1L, 2L, 3L));

        // 模拟大量语音包数据
        List<Object> mockVoicePackets = new ArrayList<>();
        for (int i = 0; i < MEDIUM_DATA_COUNT; i++) {
            Map<String, Object> packet = new HashMap<>();
            packet.put("id", (long) i);
            packet.put("name", "性能测试语音包_" + i);
            packet.put("voiceWorkId", (long) (i % 10000 + 1));
            packet.put("fileUrl", "https://test-oss.com/voice/perf_" + i + ".mp3");
            packet.put("voiceTime", 30 + (i % 180));
            mockVoicePackets.add(packet);
        }

        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class)))
                .thenReturn(mockVoicePackets);

        // When - 执行关联查询性能测试
        long startTime = System.currentTimeMillis();
        Result result = workService.getWorkList();
        long endTime = System.currentTimeMillis();

        // Then - 验证结果和性能
        assertNotNull(result, "查询结果不应为空");
        assertEquals(10000, result.getCode(), "应返回成功状态码");

        long executionTime = endTime - startTime;
        double queryRate = MEDIUM_DATA_COUNT * 1000.0 / executionTime;

        System.out.println("=== 语音包关联查询性能测试结果 ===");
        System.out.println("查询数据量: " + MEDIUM_DATA_COUNT + " 条");
        System.out.println("查询耗时: " + executionTime + "ms");
        System.out.println("查询速度: " + String.format("%.2f", queryRate) + " 条/秒");
        System.out.println("平均每条记录耗时: " + String.format("%.6f", (double) executionTime / MEDIUM_DATA_COUNT) + "ms");

        assertTrue(executionTime < PERFORMANCE_THRESHOLD_MS,
                "关联查询" + MEDIUM_DATA_COUNT + "条数据应在" + PERFORMANCE_THRESHOLD_MS + "ms内完成，实际耗时: " + executionTime
                        + "ms");

        // 性能警告
        if (executionTime > PERFORMANCE_THRESHOLD_MS / 2) {
            System.out.println("警告：关联查询性能可能需要优化，当前耗时: " + executionTime + "ms");
        }

        verify(shopRefUtil).isNeedWxFilter();
        verify(shopRefUtil).getShopRef();
        verify(jdbcTemplate, times(1)).query(anyString(), any(BeanPropertyRowMapper.class));
    }

    /**
     * 性能测试总结方法
     * 打印所有性能测试的汇总信息
     */
    @Test
    @DisplayName("数据库性能测试 - 综合性能测试总结")
    void testDatabasePerformance_Summary() {
        String separator = "============================================================";
        String dashLine = "------------------------------------------------------------";

        System.out.println("\n" + separator);
        System.out.println("           作品服务数据库性能测试总结");
        System.out.println(separator);
        System.out.println("测试项目                    | 数据量      | 性能要求");
        System.out.println(dashLine);
        System.out.println("大数据量作品列表查询        | 100,000条   | < 5秒");
        System.out.println("批量添加作品               | 1,000条     | > 100条/秒");
        System.out.println("批量更新作品               | 1,000条     | > 80条/秒");
        System.out.println("批量删除作品               | 1,000条     | > 120条/秒");
        System.out.println("并发查询作品               | 10线程      | < 5秒平均");
        System.out.println("语音包关联查询             | 50,000条    | < 5秒");
        System.out.println(dashLine);
        System.out.println("总测试数据量: " + (LARGE_DATA_COUNT + MEDIUM_DATA_COUNT) + " 条记录");
        System.out.println("测试覆盖场景: 增删改查、并发、关联查询");
        System.out.println("性能基准: 基于实际业务场景制定");
        System.out.println(separator);

        // 验证测试数据生成正常
        assertNotNull(largeVoiceWorkDataset, "大数据集应该已生成");
        assertNotNull(largeVoicePacketDataset, "语音包数据集应该已生成");
        assertEquals(LARGE_DATA_COUNT, largeVoiceWorkDataset.size(), "作品数据集大小应正确");
        assertEquals(MEDIUM_DATA_COUNT, largeVoicePacketDataset.size(), "语音包数据集大小应正确");

        System.out.println("✓ 所有性能测试数据生成完成");
        System.out.println("✓ 性能测试框架验证通过");
        System.out.println("✓ 可以开始执行具体的性能测试用例");
    }

    // ==================== 业务逻辑单元测试方法 ====================

    @Test
    @DisplayName("测试作品列表查询 - 成功场景")
    void testList_Success() {
        // Given
        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class)))
                .thenReturn(10L);

        List<VoiceWorkVo> mockResults = Arrays.asList(voiceWorkVo);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any()))
                .thenReturn(mockResults);

        // When
        Result result = workService.list(workDto, 1);

        // Then
        assertNotNull(result, "查询结果不应为空");
        assertEquals(10000, result.getCode(), "应返回成功状态码");
        verify(jdbcTemplate, times(1)).queryForObject(anyString(), eq(Long.class), any());
        verify(jdbcTemplate, times(1)).query(anyString(), any(BeanPropertyRowMapper.class), any());
    }

    @Test
    @DisplayName("测试作品列表查询 - 微信过滤场景")
    void testList_WxFilter() {
        // Given
        when(shopRefUtil.isNeedWxFilter()).thenReturn(true);

        // When
        Result result = workService.list(workDto, 1);

        // Then
        assertNotNull(result, "查询结果不应为空");
        assertEquals(10000, result.getCode(), "应返回成功状态码");
        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertEquals(0, data.get("total"), "总数应为0");
        verify(shopRefUtil, times(1)).isNeedWxFilter();
    }

    @Test
    @DisplayName("测试添加作品 - 成功场景")
    void testAdd_Success() {
        // Given
        when(voiceWorkRepository.save(any(VoiceWork.class))).thenReturn(voiceWork);
        when(voicePacketRepository.save(any(VoicePacket.class))).thenReturn(voicePacket);

        // When
        Result result = workService.add(voiceWorkVo);

        // Then
        assertNotNull(result, "添加结果不应为空");
        assertEquals(10000, result.getCode(), "应返回成功状态码");
        verify(voiceWorkRepository, times(1)).save(any(VoiceWork.class));
        verify(voicePacketRepository, times(1)).save(any(VoicePacket.class));
    }

    @Test
    @DisplayName("测试添加作品 - 参数为空")
    void testAdd_VoNull() {
        // When
        Result result = workService.add(null);

        // Then
        assertNotNull(result, "添加结果不应为空");
        assertEquals(10001, result.getCode(), "应返回错误状态码");
        assertEquals("数据不能为空", result.getMessage(), "错误信息应正确");
    }

    @Test
    @DisplayName("测试添加作品 - 标题为空")
    void testAdd_TitleBlank() {
        // Given
        voiceWorkVo.setTitle("");

        // When
        Result result = workService.add(voiceWorkVo);

        // Then
        assertNotNull(result, "添加结果不应为空");
        assertEquals(10001, result.getCode(), "应返回错误状态码");
        assertEquals("作品标题不能为空", result.getMessage(), "错误信息应正确");
    }

    @Test
    @DisplayName("测试添加作品 - 内容为空")
    void testAdd_ContentBlank() {
        // Given
        voiceWorkVo.setContent("");

        // When
        Result result = workService.add(voiceWorkVo);

        // Then
        assertNotNull(result, "添加结果不应为空");
        assertEquals(10001, result.getCode(), "应返回错误状态码");
        assertEquals("作品文字内容不能为空", result.getMessage(), "错误信息应正确");
    }

    @Test
    @DisplayName("测试添加作品 - 主播ID为空")
    void testAdd_AnchorIdNull() {
        // Given
        voiceWorkVo.setAnchorId(null);

        // When
        Result result = workService.add(voiceWorkVo);

        // Then
        assertNotNull(result, "添加结果不应为空");
        assertEquals(10001, result.getCode(), "应返回错误状态码");
        assertEquals("主播id不能为空", result.getMessage(), "错误信息应正确");
    }

    @Test
    @DisplayName("测试上传作品 - 成功场景")
    void testUploadWork_Success() {
        // Given
        File mockFile = mock(File.class);
        when(ossService.getObjectFile(null, voiceWorkVo.getFileUrl())).thenReturn(mockFile);
        when(voiceWorkRepository.save(any(VoiceWork.class))).thenReturn(voiceWork);
        when(voicePacketRepository.save(any(VoicePacket.class))).thenReturn(voicePacket);
        when(redisService.getValue(anyString())).thenReturn("24000");

        // When
        Result result = workService.uploadWork(voiceWorkVo);

        // Then
        assertNotNull(result, "上传结果不应为空");
        assertEquals(10000, result.getCode(), "应返回成功状态码");
        verify(ossService, times(1)).getObjectFile(null, voiceWorkVo.getFileUrl());
        verify(voiceWorkRepository, times(1)).save(any(VoiceWork.class));
        verify(voicePacketRepository, times(1)).save(any(VoicePacket.class));
    }

    @Test
    @DisplayName("测试上传作品 - 参数为空")
    void testUploadWork_VoNull() {
        // When
        Result result = workService.uploadWork(null);

        // Then
        assertNotNull(result, "上传结果不应为空");
        assertEquals(10001, result.getCode(), "应返回错误状态码");
        assertEquals("数据不能为空", result.getMessage(), "错误信息应正确");
    }

    @Test
    @DisplayName("测试更新作品 - 成功场景")
    void testUpdate_Success() {
        // Given
        when(voiceWorkRepository.findById(1L)).thenReturn(Optional.of(voiceWork));
        when(voiceWorkRepository.save(any(VoiceWork.class))).thenReturn(voiceWork);

        // When
        Result result = workService.update(voiceWorkVo);

        // Then
        assertNotNull(result, "更新结果不应为空");
        assertEquals(10000, result.getCode(), "应返回成功状态码");
        verify(voiceWorkRepository, times(1)).findById(1L);
        verify(voiceWorkRepository, times(1)).save(any(VoiceWork.class));
    }

    @Test
    @DisplayName("测试更新作品 - 参数为空")
    void testUpdate_VoNull() {
        // When
        Result result = workService.update(null);

        // Then
        assertNotNull(result, "更新结果不应为空");
        assertEquals(10001, result.getCode(), "应返回错误状态码");
        assertEquals("数据不能为空", result.getMessage(), "错误信息应正确");
    }

    @Test
    @DisplayName("测试更新作品 - 作品ID为空")
    void testUpdate_IdNull() {
        // Given
        voiceWorkVo.setId(null);

        // When
        Result result = workService.update(voiceWorkVo);

        // Then
        assertNotNull(result, "更新结果不应为空");
        assertEquals(10001, result.getCode(), "应返回错误状态码");
        assertEquals("作品id不能为空", result.getMessage(), "错误信息应正确");
    }

    @Test
    @DisplayName("测试更新作品 - 作品不存在")
    void testUpdate_WorkNotFound() {
        // Given
        when(voiceWorkRepository.findById(1L)).thenReturn(Optional.empty());

        // When
        Result result = workService.update(voiceWorkVo);

        // Then
        assertNotNull(result, "更新结果不应为空");
        assertEquals(10001, result.getCode(), "应返回错误状态码");
        assertEquals("作品不存在", result.getMessage(), "错误信息应正确");
    }

    @Test
    @DisplayName("测试删除作品 - 成功场景")
    void testDelete_Success() {
        // Given
        when(voicePacketRepository.findByVoiceWorkId(1L)).thenReturn(voicePacket);
        when(voicePacketRepository.findByVoiceWorkId(2L)).thenReturn(voicePacket);
        when(ossService.doesObjectExist(anyString())).thenReturn(true);
        when(voiceWorkRepository.updateDelStatusById(anyLong())).thenReturn(1);
        when(voicePacketRepository.deleteByVoiceWorkId(anyLong())).thenReturn(1);

        // When
        Result result = workService.delete(workDto);

        // Then
        assertNotNull(result, "删除结果不应为空");
        assertEquals(10000, result.getCode(), "应返回成功状态码");
        verify(voiceWorkRepository, times(2)).updateDelStatusById(anyLong());
        verify(voicePacketRepository, times(2)).deleteByVoiceWorkId(anyLong());
    }

    @Test
    @DisplayName("测试删除作品 - 参数为空")
    void testDelete_DtoNull() {
        // When
        Result result = workService.delete(null);

        // Then
        assertNotNull(result, "删除结果不应为空");
        assertEquals(10001, result.getCode(), "应返回错误状态码");
        assertEquals("数据不能为空", result.getMessage(), "错误信息应正确");
    }

    @Test
    @DisplayName("测试删除作品 - ID列表为空")
    void testDelete_IdsEmpty() {
        // Given
        workDto.setIds(new ArrayList<>());

        // When
        Result result = workService.delete(workDto);

        // Then
        assertNotNull(result, "删除结果不应为空");
        assertEquals(10001, result.getCode(), "应返回错误状态码");
        assertEquals("作品id不能为空", result.getMessage(), "错误信息应正确");
    }

    @Test
    @DisplayName("测试查找模板 - 成功场景")
    void testFindTemplates_Success() {
        // Given
        List<TemplateTypeVo> mockTemplateTypes = new ArrayList<>();
        TemplateTypeVo templateTypeVo = new TemplateTypeVo();
        templateTypeVo.setId(1L);
        templateTypeVo.setName("测试模板类型");
        mockTemplateTypes.add(templateTypeVo);

        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class)))
                .thenReturn(mockTemplateTypes);

        List<Template> mockTemplates = Arrays.asList(template);
        when(templateRepository.findByDelStatus()).thenReturn(mockTemplates);

        // When
        Result result = workService.findTemplates();

        // Then
        assertNotNull(result, "查找结果不应为空");
        assertEquals(10000, result.getCode(), "应返回成功状态码");
        verify(jdbcTemplate, times(1)).query(anyString(), any(BeanPropertyRowMapper.class));
        verify(templateRepository, times(1)).findByDelStatus();
    }

    @Test
    @DisplayName("测试查找主播 - 成功场景")
    void testFindAnchors_Success() {
        // Given
        List<Anchor> mockAnchors = Arrays.asList(anchor);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any()))
                .thenReturn(mockAnchors);

        // When
        Result result = workService.findAnchors("测试");

        // Then
        assertNotNull(result, "查找结果不应为空");
        assertEquals(10000, result.getCode(), "应返回成功状态码");
        verify(jdbcTemplate, times(1)).query(anyString(), any(BeanPropertyRowMapper.class), any());
    }

    @Test
    @DisplayName("测试查找长音频主播 - 成功场景")
    void testFindLongAnchors_Success() {
        // Given
        List<LongAnchor> mockLongAnchors = Arrays.asList(longAnchor);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any()))
                .thenReturn(mockLongAnchors);

        // When
        Result result = workService.findLongAnchors("测试");

        // Then
        assertNotNull(result, "查找结果不应为空");
        assertEquals(10000, result.getCode(), "应返回成功状态码");
        verify(jdbcTemplate, times(1)).query(anyString(), any(BeanPropertyRowMapper.class), any());
    }

    @Test
    @DisplayName("测试查找背景音乐 - 成功场景")
    void testFindBackgroundMusic_Success() {
        // Given
        List<BackGroundMusicTypeVo> mockMusicTypes = new ArrayList<>();
        BackGroundMusicTypeVo musicTypeVo = new BackGroundMusicTypeVo();
        musicTypeVo.setId(1L);
        musicTypeVo.setName("测试音乐类型");
        mockMusicTypes.add(musicTypeVo);

        List<BackGroundMusicVo> mockMusic = new ArrayList<>();
        BackGroundMusicVo music = new BackGroundMusicVo();
        music.setId(1L);
        music.setName("测试背景音乐");
        music.setTypeId(1L);
        music.setMusicUrl("test-music.mp3");
        music.setMusicTime(120);
        mockMusic.add(music);

        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class)))
                .thenReturn(mockMusicTypes).thenReturn(mockMusic);

        // When
        Result result = workService.findBackgroundMusic();

        // Then
        assertNotNull(result, "查找结果不应为空");
        assertEquals(10000, result.getCode(), "应返回成功状态码");
        verify(jdbcTemplate, times(2)).query(anyString(), any(BeanPropertyRowMapper.class));
    }

    @Test
    @DisplayName("测试查找微信用户 - 成功场景")
    void testFindWechatUsers_Success() {
        // Given
        List<WechatUser> mockUsers = new ArrayList<>();
        WechatUser user = new WechatUser();
        user.setId(1L);
        user.setNickname("测试用户");
        user.setPhone("13800138000");
        mockUsers.add(user);

        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any()))
                .thenReturn(mockUsers);

        // When
        Result result = workService.findWechatUsers("测试");

        // Then
        assertNotNull(result, "查找结果不应为空");
        assertEquals(10000, result.getCode(), "应返回成功状态码");
        verify(jdbcTemplate, times(1)).query(anyString(), any(BeanPropertyRowMapper.class), any());
    }

    @Test
    @DisplayName("测试获取作品列表 - 成功场景")
    void testGetWorkList_Success() {
        // Given
        when(shopRefUtil.isNeedWxFilter()).thenReturn(false);
        when(shopRefUtil.getShopRef()).thenReturn(Arrays.asList(1L, 2L, 3L));

        List<ShopSelectListVo> mockList = new ArrayList<>();
        ShopSelectListVo vo = new ShopSelectListVo();
        vo.setId(1L);
        vo.setName("测试作品");
        mockList.add(vo);

        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class)))
                .thenReturn(mockList);

        // When
        Result result = workService.getWorkList();

        // Then
        assertNotNull(result, "查询结果不应为空");
        assertEquals(10000, result.getCode(), "应返回成功状态码");
        verify(shopRefUtil, times(1)).isNeedWxFilter();
        verify(jdbcTemplate, times(1)).query(anyString(), any(BeanPropertyRowMapper.class));
    }

    @Test
    @DisplayName("测试获取作品列表 - 微信过滤场景")
    void testGetWorkList_WxFilter() {
        // Given
        when(shopRefUtil.isNeedWxFilter()).thenReturn(true);

        // When
        Result result = workService.getWorkList();

        // Then
        assertNotNull(result, "查询结果不应为空");
        assertEquals(10000, result.getCode(), "应返回成功状态码");
        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertEquals(0, data.get("total"), "总数应为0");
        verify(shopRefUtil, times(1)).isNeedWxFilter();
    }

    @Test
    @DisplayName("测试AI分类列表 - 成功场景")
    void testAiClassList_Success() {
        // Given
        List<AiClass> mockList = Arrays.asList(aiClass);
        when(aiClassRepository.findByDelStatus(0)).thenReturn(mockList);

        // When
        Result result = workService.aiClassList();

        // Then
        assertNotNull(result, "查询结果不应为空");
        assertEquals(10000, result.getCode(), "应返回成功状态码");
        verify(aiClassRepository, times(1)).findByDelStatus(0);
    }

    @Test
    @DisplayName("测试AI风格列表 - 成功场景")
    void testAiStyleList_Success() {
        // Given
        List<AiStyle> mockList = Arrays.asList(aiStyle);
        when(aiStyleRepository.findByDelStatus(0)).thenReturn(mockList);

        // When
        Result result = workService.aiStyleList();

        // Then
        assertNotNull(result, "查询结果不应为空");
        assertEquals(10000, result.getCode(), "应返回成功状态码");
        verify(aiStyleRepository, times(1)).findByDelStatus(0);
    }

    @Test
    @DisplayName("测试AI语言列表 - 成功场景")
    void testAiLanguageList_Success() {
        // Given
        List<AiLanguage> mockList = Arrays.asList(aiLanguage);
        when(aiLanguageRepository.findByDelStatus(0)).thenReturn(mockList);

        // When
        Result result = workService.aiLanguageList();

        // Then
        assertNotNull(result, "查询结果不应为空");
        assertEquals(10000, result.getCode(), "应返回成功状态码");
        verify(aiLanguageRepository, times(1)).findByDelStatus(0);
    }

}
